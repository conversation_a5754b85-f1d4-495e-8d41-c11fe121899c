"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useBookingConfirmation } from "@/lib/domains/local-experiences-booking"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, XCircle, Loader2 } from "lucide-react"

function BookingSuccessContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { confirmBooking, bookingConfirmation, isProcessingPayment, paymentError } =
    useBookingConfirmation()

  const [isConfirming, setIsConfirming] = useState(true)
  const [confirmationError, setConfirmationError] = useState<string | null>(null)

  const sessionId = searchParams.get("session_id")
  const bookingId = searchParams.get("booking_id")
  const experienceId = searchParams.get("experience_id")

  useEffect(() => {
    const confirmPayment = async () => {
      if (!sessionId || !bookingId || !experienceId) {
        setConfirmationError("Missing required parameters")
        setIsConfirming(false)
        return
      }

      try {
        const result = await confirmBooking(sessionId, bookingId, experienceId)
        if (!result.success) {
          setConfirmationError(result.error || "Failed to confirm booking")
        }
      } catch (error) {
        console.error("Error confirming booking:", error)
        setConfirmationError("An unexpected error occurred")
      } finally {
        setIsConfirming(false)
      }
    }

    confirmPayment()
  }, [sessionId, bookingId, experienceId, confirmBooking])

  const handleReturnToExperiences = () => {
    router.push("/experiences")
  }

  const handleViewBooking = () => {
    if (bookingConfirmation?.booking) {
      router.push(`/experiences/${experienceId}/booking/${bookingId}`)
    }
  }

  if (isConfirming || isProcessingPayment) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Loader2 className="h-12 w-12 animate-spin mx-auto text-blue-600" />
              <h2 className="text-xl font-semibold">Confirming Your Booking</h2>
              <p className="text-gray-600">
                Please wait while we confirm your payment and finalize your booking...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (confirmationError || paymentError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <CardTitle className="text-xl text-red-600">Booking Confirmation Failed</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">{confirmationError || paymentError}</p>
            <div className="flex flex-col space-y-2">
              <Button onClick={handleReturnToExperiences} className="w-full">
                Return to Experiences
              </Button>
              <Button variant="outline" onClick={() => window.location.reload()} className="w-full">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (bookingConfirmation?.booking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <CardTitle className="text-xl text-green-600">Booking Confirmed!</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center space-y-2">
              <p className="text-gray-600">
                Your experience booking has been confirmed successfully.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="font-medium">Booking ID</p>
                <p className="text-sm text-gray-600">{bookingConfirmation.booking.id}</p>
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <Button onClick={handleViewBooking} className="w-full">
                View Booking Details
              </Button>
              <Button variant="outline" onClick={handleReturnToExperiences} className="w-full">
                Browse More Experiences
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <XCircle className="h-12 w-12 text-red-500 mx-auto" />
            <h2 className="text-xl font-semibold text-red-600">Something went wrong</h2>
            <p className="text-gray-600">
              We couldn't process your booking confirmation. Please contact support.
            </p>
            <Button onClick={handleReturnToExperiences} className="w-full">
              Return to Experiences
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function BookingSuccessPage() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      }
    >
      <BookingSuccessContent />
    </Suspense>
  )
}
