"use client"

import { useEffect, useState } from "react"
import { Search, MapPin, Clock, Users, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useLocalExperiences, useExperienceModal } from "@/lib/domains/local-experiences"
import { LocalExperience } from "@/lib/domains/local-experiences"
import { ExperienceDetailModal } from "./components/experience-detail-modal"
// import { ExperienceFiltersModal } from "./components/experience-filters-modal"
import { ExperienceBookingModal } from "./components/experience-booking-modal"

export default function ExperiencesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  // const [isFiltersOpen, setIsFiltersOpen] = useState(false)

  const {
    experiences,
    searchFilters,
    isSearching,
    searchError,
    searchExperiences,
    loadAllExperiences,
    clearSearch,
  } = useLocalExperiences()

  const { openExperienceModal } = useExperienceModal()

  // Load experiences on mount
  useEffect(() => {
    loadAllExperiences()
  }, [loadAllExperiences])

  const handleSearch = () => {
    if (searchQuery.trim()) {
      searchExperiences({
        ...searchFilters,
        searchTerm: searchQuery.trim(),
      })
    } else {
      clearSearch()
      loadAllExperiences()
    }
  }

  const handleExperienceClick = (experience: LocalExperience) => {
    openExperienceModal(experience)
  }

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-6">
          <div className="space-y-4">
            {/* Title Section */}
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Local Experiences</h1>
              <p className="text-muted-foreground mt-2">
                Discover unique activities and adventures in your area
              </p>
            </div>

            {/* Search & Filter Bar */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search experiences..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                    className="pl-10"
                  />
                </div>
                <Button onClick={handleSearch} disabled={isSearching}>
                  {isSearching ? "Searching..." : "Search"}
                </Button>
              </div>
              {/* <Button
                variant="outline"
                onClick={() => setIsFiltersOpen(true)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Filters
              </Button> */}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {searchError && (
          <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-destructive">{searchError}</p>
          </div>
        )}

        {/* Experience Cards Grid */}
        {isSearching ? (
          <ExperienceGridSkeleton />
        ) : experiences.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {experiences.map((experience) => (
              <ExperienceCard
                key={experience.id}
                experience={experience}
                onClick={() => handleExperienceClick(experience)}
                formatPrice={formatPrice}
                formatDuration={formatDuration}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="mb-4">
                <Search className="h-12 w-12 text-muted-foreground mx-auto" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No experiences found</h3>
              <p className="text-muted-foreground">
                {searchQuery
                  ? "Try adjusting your search terms or filters"
                  : "Check back later for new experiences in your area"}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <ExperienceDetailModal />
      {/* <ExperienceFiltersModal open={isFiltersOpen} onOpenChange={setIsFiltersOpen} /> */}
      <ExperienceBookingModal />
    </div>
  )
}

// Experience Card Component
interface ExperienceCardProps {
  experience: LocalExperience
  onClick: () => void
  formatPrice: (price: number, currency: string) => string
  formatDuration: (minutes: number) => string
}

function ExperienceCard({ experience, onClick, formatPrice, formatDuration }: ExperienceCardProps) {
  return (
    <Card
      className="cursor-pointer hover:shadow-lg transition-shadow duration-200 overflow-hidden"
      onClick={onClick}
    >
      {/* Image Section */}
      <div className="relative h-48 bg-muted">
        {experience.images?.[0] ? (
          <img
            src={experience.images[0]}
            alt={experience.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <MapPin className="h-12 w-12 text-muted-foreground" />
          </div>
        )}

        {/* Rating Badge */}
        {experience.rating > 0 && (
          <div className="absolute top-3 right-3 bg-background/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span className="text-xs font-medium">{experience.rating.toFixed(1)}</span>
          </div>
        )}
      </div>

      <CardContent className="p-4 space-y-3">
        {/* Title */}
        <h3 className="font-semibold text-lg line-clamp-2">{experience.title}</h3>

        {/* Host Info */}
        <p className="text-sm text-muted-foreground">Hosted by {experience.host.name}</p>

        {/* Location & Duration */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            <span>{experience.location.city}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{formatDuration(experience.duration)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>Up to {experience.maxGuests}</span>
          </div>
        </div>

        {/* Description */}
        <p className="text-sm text-muted-foreground line-clamp-2">{experience.shortDescription}</p>

        {/* Categories */}
        {experience.categories.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {experience.categories.slice(0, 2).map((category) => (
              <Badge key={category} variant="secondary" className="text-xs">
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Badge>
            ))}
            {experience.categories.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{experience.categories.length - 2}
              </Badge>
            )}
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between pt-2">
          <div>
            <span className="text-lg font-semibold">
              {formatPrice(experience.pricing.basePrice, experience.pricing.currency)}
            </span>
            <span className="text-sm text-muted-foreground"> per person</span>
          </div>
          <Button size="sm">Book Now</Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Loading Skeleton
function ExperienceGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <Card key={i} className="overflow-hidden">
          <Skeleton className="h-48 w-full" />
          <CardContent className="p-4 space-y-3">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
            <div className="flex gap-4">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-14" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-12" />
            </div>
            <div className="flex justify-between items-center pt-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-8 w-20" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
