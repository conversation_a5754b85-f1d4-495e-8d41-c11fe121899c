"use client"

import { useState, useEffect } from "react"
import { X, Calendar, Users, Clock, MapPin, CreditCard } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useLocalExperiencesBooking } from "@/lib/domains/local-experiences-booking"
import { LocalExperience } from "@/lib/domains/local-experiences"

export function ExperienceBookingModal() {
  const {
    selectedExperience,
    isBookingModalOpen,
    bookingForm,
    isProcessingPayment,
    paymentError,
    closeBookingModal,
    updateBookingForm,
    processBooking,
  } = useLocalExperiencesBooking()

  const [guestCount, setGuestCount] = useState(1)
  const [selectedDate, setSelectedDate] = useState("")
  const [selectedTime, setSelectedTime] = useState("")
  const [specialRequests, setSpecialRequests] = useState("")
  const [contactEmail, setContactEmail] = useState("")
  const [contactPhone, setContactPhone] = useState("")

  // Reset form when modal opens
  useEffect(() => {
    if (isBookingModalOpen && selectedExperience) {
      setGuestCount(selectedExperience.minGuests)
      setSelectedDate("")
      setSelectedTime("")
      setSpecialRequests("")
      setContactEmail("")
      setContactPhone("")
    }
  }, [isBookingModalOpen, selectedExperience])

  if (!selectedExperience) return null

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours} hours ${remainingMinutes} minutes` : `${hours} hours`
  }

  const totalPrice = selectedExperience.pricing.basePrice * guestCount

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !contactEmail) {
      return
    }

    const bookingData = {
      experienceId: selectedExperience.id,
      guestCount,
      scheduledDate: new Date(`${selectedDate}T${selectedTime}`),
      specialRequests: specialRequests.trim() || undefined,
      contactInfo: {
        email: contactEmail,
        phone: contactPhone.trim() || undefined,
      },
      totalAmount: totalPrice,
    }

    await processBooking(bookingData)
  }

  const isFormValid =
    selectedDate &&
    selectedTime &&
    contactEmail &&
    guestCount >= selectedExperience.minGuests &&
    guestCount <= selectedExperience.maxGuests

  return (
    <Dialog open={isBookingModalOpen} onOpenChange={closeBookingModal}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Book Experience
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* Experience Summary */}
          <div className="space-y-3">
            <h3 className="font-semibold">{selectedExperience.title}</h3>
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{selectedExperience.location.city}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatDuration(selectedExperience.duration)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>
                  {selectedExperience.minGuests}-{selectedExperience.maxGuests} guests
                </span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Booking Form */}
          <div className="space-y-4">
            {/* Guest Count */}
            <div>
              <Label htmlFor="guests">Number of Guests</Label>
              <Input
                id="guests"
                type="number"
                min={selectedExperience.minGuests}
                max={selectedExperience.maxGuests}
                value={guestCount}
                onChange={(e) => setGuestCount(parseInt(e.target.value) || 1)}
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Min: {selectedExperience.minGuests}, Max: {selectedExperience.maxGuests}
              </p>
            </div>

            {/* Date Selection */}
            <div>
              <Label htmlFor="date">Preferred Date</Label>
              <Input
                id="date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                min={new Date().toISOString().split("T")[0]}
                className="mt-1"
              />
            </div>

            {/* Time Selection */}
            <div>
              <Label htmlFor="time">Preferred Time</Label>
              <Input
                id="time"
                type="time"
                value={selectedTime}
                onChange={(e) => setSelectedTime(e.target.value)}
                className="mt-1"
              />
            </div>

            {/* Contact Information */}
            <div>
              <Label htmlFor="email">Contact Email *</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={contactEmail}
                onChange={(e) => setContactEmail(e.target.value)}
                className="mt-1"
                required
              />
            </div>

            <div>
              <Label htmlFor="phone">Contact Phone (Optional)</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="+****************"
                value={contactPhone}
                onChange={(e) => setContactPhone(e.target.value)}
                className="mt-1"
              />
            </div>

            {/* Special Requests */}
            <div>
              <Label htmlFor="requests">Special Requests (Optional)</Label>
              <Textarea
                id="requests"
                placeholder="Any special requirements or requests..."
                value={specialRequests}
                onChange={(e) => setSpecialRequests(e.target.value)}
                className="mt-1"
                rows={3}
              />
            </div>
          </div>

          <Separator />

          {/* Price Summary */}
          <div className="space-y-3">
            <h4 className="font-semibold">Price Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>
                  {formatPrice(
                    selectedExperience.pricing.basePrice,
                    selectedExperience.pricing.currency
                  )}{" "}
                  × {guestCount} guest{guestCount > 1 ? "s" : ""}
                </span>
                <span>{formatPrice(totalPrice, selectedExperience.pricing.currency)}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>{formatPrice(totalPrice, selectedExperience.pricing.currency)}</span>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {paymentError && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm">{paymentError}</p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t">
          <Button variant="outline" onClick={closeBookingModal} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={handleBooking}
            disabled={!isFormValid || isProcessingPayment}
            className="flex-1"
          >
            {isProcessingPayment
              ? "Processing..."
              : `Pay ${formatPrice(totalPrice, selectedExperience.pricing.currency)}`}
          </Button>
        </div>

        {/* Terms */}
        <div className="text-center text-xs text-muted-foreground">
          <p>By booking, you agree to our terms of service and cancellation policy.</p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
