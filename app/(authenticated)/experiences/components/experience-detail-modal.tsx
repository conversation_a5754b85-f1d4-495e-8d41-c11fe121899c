"use client"

import { useState, useEffect } from "react"
import { X, MapP<PERSON>, Clock, <PERSON>, Star, Calendar, Check, Minus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useLocalExperience, useExperienceModal, useExperienceBooking } from "@/lib/domains/local-experiences"
import { Dialog, DialogContent } from "@/components/ui/dialog"

export function ExperienceDetailModal() {
  const { selectedExperience, isExperienceModalOpen, closeModal } = useExperienceModal()
  const { selectedExperienceAvailability, loadExperienceAvailability } = useLocalExperience()
  const { openBookingModal } = useExperienceBooking()
  
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)

  // Load availability when experience is selected
  useEffect(() => {
    if (selectedExperience?.id) {
      loadExperienceAvailability(selectedExperience.id)
    }
  }, [selectedExperience?.id, loadExperienceAvailability])

  if (!selectedExperience) return null

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
    }).format(price)
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes} minutes`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours} hours ${remainingMinutes} minutes` : `${hours} hours`
  }

  const handleBookNow = () => {
    closeModal()
    openBookingModal(selectedExperience)
  }

  return (
    <Dialog open={isExperienceModalOpen} onOpenChange={closeModal}>
      <DialogContent className="max-w-6xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex h-full">
          {/* Left Side - Content (60%) */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b">
              <div className="flex-1">
                <h2 className="text-2xl font-bold">{selectedExperience.title}</h2>
                <p className="text-muted-foreground mt-1">
                  Hosted by {selectedExperience.host.name}
                </p>
              </div>
              <Button variant="ghost" size="icon" onClick={closeModal}>
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Scrollable Content */}
            <ScrollArea className="flex-1">
              <div className="p-6 space-y-6">
                {/* Image Gallery */}
                <div className="space-y-4">
                  {/* Main Image */}
                  <div className="relative h-64 bg-muted rounded-lg overflow-hidden">
                    {selectedExperience.images?.[selectedImageIndex] ? (
                      <img
                        src={selectedExperience.images[selectedImageIndex]}
                        alt={selectedExperience.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <MapPin className="h-16 w-16 text-muted-foreground" />
                      </div>
                    )}
                  </div>

                  {/* Thumbnail Gallery */}
                  {selectedExperience.images && selectedExperience.images.length > 1 && (
                    <div className="flex gap-2 overflow-x-auto">
                      {selectedExperience.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 ${
                            index === selectedImageIndex
                              ? "border-primary"
                              : "border-transparent"
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${selectedExperience.title} ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Quick Info */}
                <div className="flex flex-wrap gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedExperience.location.city}, {selectedExperience.location.country}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>{formatDuration(selectedExperience.duration)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedExperience.minGuests}-{selectedExperience.maxGuests} guests</span>
                  </div>
                  {selectedExperience.rating > 0 && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span>{selectedExperience.rating.toFixed(1)} ({selectedExperience.reviewCount} reviews)</span>
                    </div>
                  )}
                </div>

                {/* Categories */}
                {selectedExperience.categories.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {selectedExperience.categories.map((category) => (
                      <Badge key={category} variant="secondary">
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </Badge>
                    ))}
                  </div>
                )}

                <Separator />

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">About this experience</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {selectedExperience.description}
                  </p>
                </div>

                <Separator />

                {/* What's Included */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">What's included</h3>
                  <div className="space-y-2">
                    {selectedExperience.inclusions.map((inclusion, index) => (
                      <div key={index} className="flex items-center gap-3">
                        {inclusion.included ? (
                          <Check className="h-4 w-4 text-green-600" />
                        ) : (
                          <Minus className="h-4 w-4 text-muted-foreground" />
                        )}
                        <span className={inclusion.included ? "" : "text-muted-foreground"}>
                          {inclusion.item}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Host Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Meet your host</h3>
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-semibold">
                        {selectedExperience.host.name.charAt(0)}
                      </span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{selectedExperience.host.name}</h4>
                      {selectedExperience.host.bio && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedExperience.host.bio}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Cancellation Policy */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Cancellation policy</h3>
                  <p className="text-muted-foreground">
                    {selectedExperience.cancellationPolicy}
                  </p>
                </div>
              </div>
            </ScrollArea>
          </div>

          {/* Right Side - Booking Panel (40%) */}
          <div className="w-2/5 border-l bg-muted/30">
            <div className="sticky top-0 p-6 space-y-6">
              {/* Price */}
              <div className="text-center">
                <div className="text-3xl font-bold">
                  {formatPrice(selectedExperience.pricing.basePrice, selectedExperience.pricing.currency)}
                </div>
                <div className="text-muted-foreground">per person</div>
              </div>

              {/* Availability Status */}
              <div className="text-center">
                {selectedExperienceAvailability ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      <span className="text-green-600 font-medium">Available</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {selectedExperienceAvailability.availableSlots} slots available
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Checking availability...</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Book Now Button */}
              <Button 
                onClick={handleBookNow}
                className="w-full"
                size="lg"
                disabled={!selectedExperienceAvailability || selectedExperienceAvailability.availableSlots === 0}
              >
                {selectedExperienceAvailability?.availableSlots === 0 ? "Fully Booked" : "Book Now"}
              </Button>

              {/* Additional Info */}
              <div className="text-center text-sm text-muted-foreground">
                <p>Free cancellation up to 24 hours before</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
