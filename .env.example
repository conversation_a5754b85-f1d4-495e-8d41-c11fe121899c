# APPLICATION
NEXT_PUBLIC_APP_URL=

# OP<PERSON><PERSON>I
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4.1-mini

# FIREBASE
NEXT_PUBLIC_FIREBASE_API_KEY=
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=
NEXT_PUBLIC_FIREBASE_PROJECT_ID=
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=
NEXT_PUBLIC_FIREBASE_APP_ID=
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=

# FIREBASE ADMIN SDK (for server-side authentication)
# Generate this from Firebase Console > Project Settings > Service Accounts > Generate New Private Key
# Then paste the JSON content here as a string (escape quotes if needed)
FIREBASE_SERVICE_ACCOUNT_KEY=

# WEATHER API
WEATHER_API_KEY=

# GOOGLE PLACES API
GOOGLE_PLACES_API_KEY=

# STRIPE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=
NEXT_PUBLIC_STRIPE_PRICE_ID_MONTHLY=
NEXT_PUBLIC_STRIPE_PRICE_ID_YEARLY=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=
# Note: Single webhook endpoint handles both subscriptions and experience bookings
# Configure webhook to listen for: checkout.session.completed, customer.subscription.updated,
# customer.subscription.deleted, invoice.payment_succeeded, invoice.payment_failed,
# payment_intent.succeeded, payment_intent.payment_failed, checkout.session.expired

# BREVO
BREVO_API_KEY=
BREVO_INVITATION_TEMPLATE_ID=166