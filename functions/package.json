{"name": "togeda-functions", "version": "1.0.0", "description": "Firebase Functions for Togeda.ai", "main": "src/index.js", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0"}, "private": true}