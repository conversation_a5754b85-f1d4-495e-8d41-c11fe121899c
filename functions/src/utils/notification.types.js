/**
 * Notification types for Firebase Functions
 * This is a JavaScript version of the TypeScript types from the main app
 * We need to duplicate these here because Firebase Functions can't access the parent directory
 */

/**
 * Available notification types
 */
const NotificationTypes = {
  MESSAGE_MENTION: "message_mention",
  TRIP_UPDATE: "trip_update", 
  TASK_ASSIGNED: "task_assigned",
  INVITATION: "invitation",
  TRIP_COMPLETED: "trip_completed",
  SQUAD_MEMBER_JOINED: "squad_member_joined",
};

/**
 * Validates if a notification type is valid
 * @param {string} type - The notification type to validate
 * @returns {boolean} - True if valid, false otherwise
 */
function isValidNotificationType(type) {
  return Object.values(NotificationTypes).includes(type);
}

/**
 * Creates a notification data object with proper structure
 * @param {Object} params - Notification parameters
 * @param {string} params.userId - User ID
 * @param {string} params.type - Notification type (use NotificationTypes constants)
 * @param {string} params.title - Notification title
 * @param {string} params.message - Notification message
 * @param {boolean} params.read - Whether notification is read (default: false)
 * @param {string} params.actionUrl - URL to navigate when notification is clicked
 * @param {string} params.relatedEntityId - ID of related entity (tripId, squadId, etc.)
 * @param {string} [params.relatedEntityType] - Type of related entity ("trip", "squad", etc.)
 * @param {string} [params.senderUserId] - ID of user who triggered notification
 * @param {string} [params.senderUserName] - Name of user who triggered notification
 * @param {string} [params.senderUserPhotoURL] - Photo URL of user who triggered notification
 * @returns {Object} - Properly structured notification data
 */
function createNotificationData({
  userId,
  type,
  title,
  message,
  read = false,
  actionUrl,
  relatedEntityId,
  relatedEntityType,
  senderUserId,
  senderUserName,
  senderUserPhotoURL,
}) {
  // Validate required fields
  if (!userId || typeof userId !== "string") {
    throw new Error("userId is required and must be a string");
  }
  
  if (!isValidNotificationType(type)) {
    throw new Error(`Invalid notification type: ${type}. Must be one of: ${Object.values(NotificationTypes).join(", ")}`);
  }
  
  if (!title || typeof title !== "string") {
    throw new Error("title is required and must be a string");
  }
  
  if (!message || typeof message !== "string") {
    throw new Error("message is required and must be a string");
  }
  
  if (!actionUrl || typeof actionUrl !== "string") {
    throw new Error("actionUrl is required and must be a string");
  }
  
  if (!relatedEntityId || typeof relatedEntityId !== "string") {
    throw new Error("relatedEntityId is required and must be a string");
  }

  // Create the notification object, filtering out undefined values
  const notification = {
    userId,
    type,
    title,
    message,
    read,
    actionUrl,
    relatedEntityId,
  };

  // Add optional fields only if they have values
  if (relatedEntityType) notification.relatedEntityType = relatedEntityType;
  if (senderUserId) notification.senderUserId = senderUserId;
  if (senderUserName) notification.senderUserName = senderUserName;
  if (senderUserPhotoURL) notification.senderUserPhotoURL = senderUserPhotoURL;

  return notification;
}

module.exports = {
  NotificationTypes,
  isValidNotificationType,
  createNotificationData,
};
