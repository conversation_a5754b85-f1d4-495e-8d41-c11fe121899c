const admin = require("firebase-admin")
const { NotificationTypes, createNotificationData } = require("./notification.types")

/**
 * Notification service for Firebase Functions
 * Server-side implementation using Firebase Admin SDK
 */
class NotificationService {
  /**
   * Create a new notification for a user
   * @param {string} userId - User ID to create notification for
   * @param {Object} notificationData - Notification data (should be created using createNotificationData)
   * @returns {Promise<string>} - The created notification ID
   */
  static async createNotification(userId, notificationData) {
    try {
      const db = admin.firestore()
      const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()

      const notification = {
        ...notificationData,
        id: notificationRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      }

      await notificationRef.set(notification)

      console.log(`Notification created for user ${userId}:`, notificationRef.id)
      return notificationRef.id
    } catch (error) {
      console.error("Error creating notification:", error)
      throw error
    }
  }

  /**
   * Create multiple notifications in batch
   * @param {Array<{userId: string, notificationData: Object}>} notifications - Array of notification objects
   * @returns {Promise<Array<string>>} - Array of created notification IDs
   */
  static async createBatchNotifications(notifications) {
    try {
      const db = admin.firestore()
      const batch = db.batch()
      const notificationIds = []

      for (const { userId, notificationData } of notifications) {
        const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()

        // Filter out undefined values to prevent Firebase errors
        const cleanNotificationData = Object.fromEntries(
          Object.entries(notificationData).filter(([_, value]) => value !== undefined)
        )

        const notification = {
          ...cleanNotificationData,
          userId,
          id: notificationRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        }

        batch.set(notificationRef, notification)
        notificationIds.push(notificationRef.id)
      }

      await batch.commit()

      console.log(`Batch notifications created for ${notifications.length} users:`, notificationIds)
      return notificationIds
    } catch (error) {
      console.error("Error creating batch notifications:", error)
      throw error
    }
  }

  /**
   * Create squad member joined notifications for all squad members except the new member
   * @param {string} squadId - Squad ID
   * @param {string} newMemberUserId - New member's user ID
   * @param {string} newMemberName - New member's display name
   * @param {string} newMemberPhotoURL - New member's photo URL (optional)
   * @param {string} squadName - Squad name
   * @returns {Promise<Array<string>>} - Array of created notification IDs
   */
  static async createSquadMemberJoinedNotifications(
    squadId,
    newMemberUserId,
    newMemberName,
    newMemberPhotoURL,
    squadName
  ) {
    try {
      // Validate required parameters
      if (!squadId || !newMemberUserId || !newMemberName || !squadName) {
        throw new Error("Missing required parameters for squad member joined notifications")
      }
      const db = admin.firestore()

      // Get all squad members except the new member
      const membersSnapshot = await db
        .collection("squads")
        .doc(squadId)
        .collection("members")
        .where("userId", "!=", newMemberUserId)
        .get()

      if (membersSnapshot.empty) {
        console.log("No existing members to notify in squad:", squadId)
        return []
      }

      // Prepare notifications for all existing members
      const notifications = membersSnapshot.docs.map((memberDoc) => {
        const memberData = memberDoc.data()

        const notificationData = createNotificationData({
          userId: memberData.userId,
          type: NotificationTypes.SQUAD_MEMBER_JOINED,
          title: "New Squad Member",
          message: `${newMemberName} joined ${squadName}`,
          read: false,
          actionUrl: `/squads/${squadId}`,
          relatedEntityId: squadId,
          relatedEntityType: "squad",
          senderUserId: newMemberUserId,
          senderUserName: newMemberName,
          senderUserPhotoURL: newMemberPhotoURL,
        })

        return {
          userId: memberData.userId,
          notificationData,
        }
      })

      // Create all notifications in batch
      const notificationIds = await this.createBatchNotifications(notifications)

      console.log(
        `Created ${notificationIds.length} squad member joined notifications for squad ${squadId}`
      )
      return notificationIds
    } catch (error) {
      console.error("Error creating squad member joined notifications:", error)
      throw error
    }
  }
}

module.exports = NotificationService
