const functions = require("firebase-functions");
const admin = require("firebase-admin");
const NotificationService = require("../utils/notification.service");

/**
 * Firebase Function that triggers when a new member is added to a squad
 * Sends in-app notifications to all existing squad members
 */
exports.onSquadMemberJoin = functions.firestore
  .document("squads/{squadId}/members/{userId}")
  .onCreate(async (snap, context) => {
    try {
      const {squadId, userId} = context.params;
      const memberData = snap.data();
      
      console.log(`New squad member added: ${userId} to squad ${squadId}`);
      
      // Get the new member's user data
      const db = admin.firestore();
      const userDoc = await db.collection("users").doc(userId).get();
      
      if (!userDoc.exists) {
        console.error(`User document not found for userId: ${userId}`);
        return;
      }
      
      const userData = userDoc.data();
      const newMemberName = userData.displayName || userData.email || "Unknown User";
      const newMemberPhotoURL = userData.photoURL;
      
      // Get squad data
      const squadDoc = await db.collection("squads").doc(squadId).get();
      
      if (!squadDoc.exists) {
        console.error(`Squad document not found for squadId: ${squadId}`);
        return;
      }
      
      const squadData = squadDoc.data();
      const squadName = squadData.name || "Unknown Squad";
      
      console.log(`Processing notifications for new member ${newMemberName} joining ${squadName}`);
      
      // Create notifications for all existing squad members (excluding the new member)
      const notificationIds = await NotificationService.createSquadMemberJoinedNotifications(
        squadId,
        userId,
        newMemberName,
        newMemberPhotoURL,
        squadName
      );
      
      console.log(`Successfully created ${notificationIds.length} notifications for squad member join`);
      
      return {
        success: true,
        squadId,
        newMemberUserId: userId,
        newMemberName,
        squadName,
        notificationsCreated: notificationIds.length,
        notificationIds,
      };
    } catch (error) {
      console.error("Error in onSquadMemberJoin function:", error);
      
      // Don't throw the error to prevent function retries
      // Log the error and return a failure response
      return {
        success: false,
        error: error.message,
        squadId: context.params.squadId,
        userId: context.params.userId,
      };
    }
  });
