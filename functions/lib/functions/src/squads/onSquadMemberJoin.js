"use strict"
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k
        var desc = Object.getOwnPropertyDescriptor(m, k)
        if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k]
            },
          }
        }
        Object.defineProperty(o, k2, desc)
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k
        o[k2] = m[k]
      })
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, "default", { enumerable: true, value: v })
      }
    : function (o, v) {
        o["default"] = v
      })
var __importStar =
  (this && this.__importStar) ||
  (function () {
    var ownKeys = function (o) {
      ownKeys =
        Object.getOwnPropertyNames ||
        function (o) {
          var ar = []
          for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k
          return ar
        }
      return ownKeys(o)
    }
    return function (mod) {
      if (mod && mod.__esModule) return mod
      var result = {}
      if (mod != null)
        for (var k = ownKeys(mod), i = 0; i < k.length; i++)
          if (k[i] !== "default") __createBinding(result, mod, k[i])
      __setModuleDefault(result, mod)
      return result
    }
  })()
Object.defineProperty(exports, "__esModule", { value: true })
exports.onSquadMemberJoin = void 0
const functions = __importStar(require("firebase-functions"))
const admin = __importStar(require("firebase-admin"))
const notification_service_1 = require("../utils/notification.service")
/**
 * Firebase Function that triggers when a new member is added to a squad
 * Sends in-app notifications to all existing squad members
 */
exports.onSquadMemberJoin = functions.firestore
  .document("squads/{squadId}/members/{userId}")
  .onCreate(async (snap, context) => {
    try {
      const { squadId, userId } = context.params
      const memberData = snap.data()
      console.log(`New squad member added: ${userId} to squad ${squadId}`)
      // Get the new member's user data
      const db = admin.firestore()
      const userDoc = await db.collection("users").doc(userId).get()
      if (!userDoc.exists) {
        console.error(`User document not found for userId: ${userId}`)
        return {
          success: false,
          error: "User document not found",
          squadId,
          userId,
        }
      }
      const userData = userDoc.data()
      const newMemberName = userData.displayName || userData.email || "Unknown User"
      const newMemberPhotoURL = userData.photoURL
      // Get squad data
      const squadDoc = await db.collection("squads").doc(squadId).get()
      if (!squadDoc.exists) {
        console.error(`Squad document not found for squadId: ${squadId}`)
        return {
          success: false,
          error: "Squad document not found",
          squadId,
          userId,
        }
      }
      const squadData = squadDoc.data()
      const squadName = squadData.name || "Unknown Squad"
      console.log(`Processing notifications for new member ${newMemberName} joining ${squadName}`)
      // Create notifications for all existing squad members (excluding the new member)
      const notificationIds =
        await notification_service_1.NotificationService.createSquadMemberJoinedNotifications(
          squadId,
          userId,
          newMemberName,
          newMemberPhotoURL,
          squadName
        )
      console.log(
        `Successfully created ${notificationIds.length} notifications for squad member join`
      )
      return {
        success: true,
        squadId,
        newMemberUserId: userId,
        newMemberName,
        squadName,
        notificationsCreated: notificationIds.length,
        notificationIds,
      }
    } catch (error) {
      console.error("Error in onSquadMemberJoin function:", error)
      // Don't throw the error to prevent function retries
      // Log the error and return a failure response
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        squadId: context.params.squadId,
        userId: context.params.userId,
      }
    }
  })
//# sourceMappingURL=onSquadMemberJoin.js.map
