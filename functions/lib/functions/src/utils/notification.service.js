"use strict"
var __createBinding =
  (this && this.__createBinding) ||
  (Object.create
    ? function (o, m, k, k2) {
        if (k2 === undefined) k2 = k
        var desc = Object.getOwnPropertyDescriptor(m, k)
        if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
          desc = {
            enumerable: true,
            get: function () {
              return m[k]
            },
          }
        }
        Object.defineProperty(o, k2, desc)
      }
    : function (o, m, k, k2) {
        if (k2 === undefined) k2 = k
        o[k2] = m[k]
      })
var __setModuleDefault =
  (this && this.__setModuleDefault) ||
  (Object.create
    ? function (o, v) {
        Object.defineProperty(o, "default", { enumerable: true, value: v })
      }
    : function (o, v) {
        o["default"] = v
      })
var __importStar =
  (this && this.__importStar) ||
  (function () {
    var ownKeys = function (o) {
      ownKeys =
        Object.getOwnPropertyNames ||
        function (o) {
          var ar = []
          for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k
          return ar
        }
      return ownKeys(o)
    }
    return function (mod) {
      if (mod && mod.__esModule) return mod
      var result = {}
      if (mod != null)
        for (var k = ownKeys(mod), i = 0; i < k.length; i++)
          if (k[i] !== "default") __createBinding(result, mod, k[i])
      __setModuleDefault(result, mod)
      return result
    }
  })()
Object.defineProperty(exports, "__esModule", { value: true })
exports.NotificationService = void 0
const admin = __importStar(require("firebase-admin"))
const notification_types_1 = require("./notification.types")
/**
 * Notification service for Firebase Functions
 * Server-side implementation using Firebase Admin SDK
 */
class NotificationService {
  /**
   * Create a new notification for a user
   */
  static async createNotification(userId, notificationData) {
    try {
      const db = admin.firestore()
      const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()
      const notification = Object.assign(Object.assign({}, notificationData), {
        id: notificationRef.id,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
      })
      await notificationRef.set(notification)
      console.log(`Notification created for user ${userId}:`, notificationRef.id)
      return notificationRef.id
    } catch (error) {
      console.error("Error creating notification:", error)
      throw error
    }
  }
  /**
   * Create multiple notifications in batch
   */
  static async createBatchNotifications(notifications) {
    try {
      const db = admin.firestore()
      const batch = db.batch()
      const notificationIds = []
      for (const { userId, notificationData } of notifications) {
        const notificationRef = db.collection("users").doc(userId).collection("notifications").doc()
        // Filter out undefined values to prevent Firebase errors
        const cleanNotificationData = Object.fromEntries(
          Object.entries(notificationData).filter(([_, value]) => value !== undefined)
        )
        const notification = Object.assign(Object.assign({}, cleanNotificationData), {
          userId,
          id: notificationRef.id,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        })
        batch.set(notificationRef, notification)
        notificationIds.push(notificationRef.id)
      }
      await batch.commit()
      console.log(`Batch notifications created for ${notifications.length} users:`, notificationIds)
      return notificationIds
    } catch (error) {
      console.error("Error creating batch notifications:", error)
      throw error
    }
  }
  /**
   * Create squad member joined notifications for all squad members except the new member
   */
  static async createSquadMemberJoinedNotifications(
    squadId,
    newMemberUserId,
    newMemberName,
    newMemberPhotoURL,
    squadName
  ) {
    try {
      // Validate required parameters
      if (!squadId || !newMemberUserId || !newMemberName || !squadName) {
        throw new Error("Missing required parameters for squad member joined notifications")
      }
      const db = admin.firestore()
      // Get all squad members except the new member
      const membersSnapshot = await db
        .collection("squads")
        .doc(squadId)
        .collection("members")
        .where("userId", "!=", newMemberUserId)
        .get()
      if (membersSnapshot.empty) {
        console.log("No existing members to notify in squad:", squadId)
        return []
      }
      // Prepare notifications for all existing members
      const notifications = membersSnapshot.docs.map((memberDoc) => {
        const memberData = memberDoc.data()
        const notificationData = (0, notification_types_1.createNotificationData)({
          userId: memberData.userId,
          type: notification_types_1.NotificationTypes.SQUAD_MEMBER_JOINED,
          title: "New Squad Member",
          message: `${newMemberName} joined ${squadName}`,
          read: false,
          actionUrl: `/squads/${squadId}`,
          relatedEntityId: squadId,
          relatedEntityType: "squad",
          senderUserId: newMemberUserId,
          senderUserName: newMemberName,
          senderUserPhotoURL: newMemberPhotoURL,
        })
        return {
          userId: memberData.userId,
          notificationData,
        }
      })
      // Create all notifications in batch
      const notificationIds = await this.createBatchNotifications(notifications)
      console.log(
        `Created ${notificationIds.length} squad member joined notifications for squad ${squadId}`
      )
      return notificationIds
    } catch (error) {
      console.error("Error creating squad member joined notifications:", error)
      throw error
    }
  }
}
exports.NotificationService = NotificationService
//# sourceMappingURL=notification.service.js.map
