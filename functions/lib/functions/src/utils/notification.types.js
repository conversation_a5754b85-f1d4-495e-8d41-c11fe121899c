"use strict"
/**
 * Re-export types from the main app with Firebase Admin SDK compatibility
 */
Object.defineProperty(exports, "__esModule", { value: true })
exports.NotificationTypes = void 0
exports.isValidNotificationType = isValidNotificationType
exports.createNotificationData = createNotificationData
/**
 * Available notification types as constants
 */
exports.NotificationTypes = {
  MESSAGE_MENTION: "message_mention",
  TRIP_UPDATE: "trip_update",
  TASK_ASSIGNED: "task_assigned",
  INVITATION: "invitation",
  TRIP_COMPLETED: "trip_completed",
  SQUAD_MEMBER_JOINED: "squad_member_joined",
}
/**
 * Validates if a notification type is valid
 */
function isValidNotificationType(type) {
  return Object.values(exports.NotificationTypes).includes(type)
}
/**
 * Creates a notification data object with proper structure and validation
 */
function createNotificationData(params) {
  const {
    userId,
    type,
    title,
    message,
    read = false,
    actionUrl,
    relatedEntityId,
    relatedEntityType,
    senderUserId,
    senderUserName,
    senderUserPhotoURL,
  } = params
  // Validate required fields
  if (!userId || typeof userId !== "string") {
    throw new Error("userId is required and must be a string")
  }
  if (!isValidNotificationType(type)) {
    throw new Error(
      `Invalid notification type: ${type}. Must be one of: ${Object.values(exports.NotificationTypes).join(", ")}`
    )
  }
  if (!title || typeof title !== "string") {
    throw new Error("title is required and must be a string")
  }
  if (!message || typeof message !== "string") {
    throw new Error("message is required and must be a string")
  }
  if (!actionUrl || typeof actionUrl !== "string") {
    throw new Error("actionUrl is required and must be a string")
  }
  if (!relatedEntityId || typeof relatedEntityId !== "string") {
    throw new Error("relatedEntityId is required and must be a string")
  }
  // Create the notification object
  const notification = {
    userId,
    type,
    title,
    message,
    read,
    actionUrl,
    relatedEntityId,
  }
  // Add optional fields only if they have values
  if (relatedEntityType) notification.relatedEntityType = relatedEntityType
  if (senderUserId) notification.senderUserId = senderUserId
  if (senderUserName) notification.senderUserName = senderUserName
  if (senderUserPhotoURL) notification.senderUserPhotoURL = senderUserPhotoURL
  return notification
}
//# sourceMappingURL=notification.types.js.map
