# Togeda Firebase Functions

This directory contains Firebase Functions for the Togeda.ai application.

## Structure

```
functions/
├── src/
│   ├── index.js              # Main entry point that exports all functions
│   ├── squads/               # Squad-related functions
│   │   └── onSquadMemberJoin.js    # Triggers when new members join squads
│   └── utils/                # Shared utilities
│       └── notification.service.js # Notification service for functions
├── package.json              # Dependencies and scripts
├── .eslintrc.js             # ESLint configuration
└── README.md                # This file
```

## Functions

### onSquadMemberJoin

**Trigger**: `squads/{squadId}/members/{userId}` document creation

**Purpose**: Sends in-app notifications to all existing squad members when a new member joins.

**Flow**:
1. Triggered when a new document is created in `squads/{squadId}/members/{userId}`
2. Fetches the new member's user data (name, photo)
3. Fetches the squad data (name)
4. Creates notifications for all existing squad members (excluding the new member)
5. Uses batch writes for efficient notification creation

## Setup

### Local Development

1. Install dependencies:
```bash
cd functions
npm install
```

2. Start the Firebase emulator:
```bash
# From project root
firebase emulators:start
```

3. The functions will be available at `http://localhost:5001`

### Production Deployment

1. Deploy functions:
```bash
# From project root
firebase deploy --only functions
```

2. Or deploy specific function:
```bash
firebase deploy --only functions:onSquadMemberJoin
```

## Environment Variables

The functions use the Firebase Admin SDK which automatically uses the project's service account in production. For local development, make sure you have:

- Firebase CLI installed and logged in
- Project configured with `firebase use <project-id>`

## Monitoring

- View function logs: `firebase functions:log`
- Monitor in Firebase Console: Functions section
- Check function performance and errors in the Firebase Console

## Dependencies

- `firebase-admin`: Server-side Firebase SDK
- `firebase-functions`: Firebase Functions SDK
- `eslint`: Code linting

## Notification Types

The functions create notifications with type `"squad_member_joined"` that include:
- Title: "New Squad Member"
- Message: "{memberName} joined {squadName}"
- Action URL: `/squads/{squadId}`
- Sender information (new member's details)
