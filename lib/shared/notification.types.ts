/**
 * Shared notification types that can be used by both the main app and Firebase Functions
 * This avoids duplication while handling Firebase SDK differences
 */

/**
 * Notification type enum
 */
export type NotificationType =
  | "message_mention"
  | "trip_update"
  | "task_assigned"
  | "invitation"
  | "trip_completed"
  | "squad_member_joined";

/**
 * Available notification types as constants
 */
export const NotificationTypes = {
  MESSAGE_MENTION: "message_mention" as const,
  TRIP_UPDATE: "trip_update" as const,
  TASK_ASSIGNED: "task_assigned" as const,
  INVITATION: "invitation" as const,
  TRIP_COMPLETED: "trip_completed" as const,
  SQUAD_MEMBER_JOINED: "squad_member_joined" as const,
} as const;

/**
 * Base notification data (without Firebase-specific timestamp types)
 */
export interface BaseNotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  actionUrl: string;
  relatedEntityId: string;
  relatedEntityType?: string;
  senderUserId?: string;
  senderUserName?: string;
  senderUserPhotoURL?: string;
}

/**
 * Base notification with ID (without timestamps)
 */
export interface BaseNotification extends BaseNotificationData {
  id: string;
}

/**
 * Validation function
 */
export function isValidNotificationType(type: string): type is NotificationType {
  return Object.values(NotificationTypes).includes(type as NotificationType);
}

/**
 * Create notification data helper
 */
export function createBaseNotificationData(params: {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  read?: boolean;
  actionUrl: string;
  relatedEntityId: string;
  relatedEntityType?: string;
  senderUserId?: string;
  senderUserName?: string;
  senderUserPhotoURL?: string;
}): BaseNotificationData {
  const {
    userId,
    type,
    title,
    message,
    read = false,
    actionUrl,
    relatedEntityId,
    relatedEntityType,
    senderUserId,
    senderUserName,
    senderUserPhotoURL,
  } = params;

  // Validate required fields
  if (!userId || typeof userId !== "string") {
    throw new Error("userId is required and must be a string");
  }

  if (!isValidNotificationType(type)) {
    throw new Error(`Invalid notification type: ${type}. Must be one of: ${Object.values(NotificationTypes).join(", ")}`);
  }

  if (!title || typeof title !== "string") {
    throw new Error("title is required and must be a string");
  }

  if (!message || typeof message !== "string") {
    throw new Error("message is required and must be a string");
  }

  if (!actionUrl || typeof actionUrl !== "string") {
    throw new Error("actionUrl is required and must be a string");
  }

  if (!relatedEntityId || typeof relatedEntityId !== "string") {
    throw new Error("relatedEntityId is required and must be a string");
  }

  // Create the notification object
  const notification: BaseNotificationData = {
    userId,
    type,
    title,
    message,
    read,
    actionUrl,
    relatedEntityId,
  };

  // Add optional fields only if they have values
  if (relatedEntityType) notification.relatedEntityType = relatedEntityType;
  if (senderUserId) notification.senderUserId = senderUserId;
  if (senderUserName) notification.senderUserName = senderUserName;
  if (senderUserPhotoURL) notification.senderUserPhotoURL = senderUserPhotoURL;

  return notification;
}
