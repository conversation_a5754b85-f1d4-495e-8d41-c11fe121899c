import { useCallback } from "react"
import { toast } from "sonner"
import { LocalExperiencesService } from "./local-experiences.service"
import { useLocalExperiencesStore } from "./local-experiences.store"
import { ExperienceSearchFilters, LocalExperience } from "./local-experiences.types"

/**
 * Hook for searching and managing local experiences
 */
export const useLocalExperiences = () => {
  const {
    experiences,
    searchFilters,
    searchResult,
    isSearching,
    searchError,
    setExperiences,
    setSearchFilters,
    setSearchResult,
    setIsSearching,
    setSearchError,
    clearSearchResults,
  } = useLocalExperiencesStore()

  /**
   * Search experiences with filters
   */
  const searchExperiences = useCallback(
    async (filters: ExperienceSearchFilters, loadMore: boolean = false) => {
      try {
        setIsSearching(true)
        setSearchError(null)

        const response = await LocalExperiencesService.searchExperiences(
          filters,
          10, // Page size
          loadMore && searchResult ? undefined : undefined // TODO: Implement pagination with lastDoc
        )

        if (response.success && response.data) {
          if (loadMore && searchResult) {
            // Append to existing results
            const updatedExperiences = [...experiences, ...response.data.experiences]
            setExperiences(updatedExperiences)
            setSearchResult({
              ...response.data,
              experiences: updatedExperiences,
            })
          } else {
            // Replace existing results
            setExperiences(response.data.experiences)
            setSearchResult(response.data)
          }
          setSearchFilters(filters)
        } else {
          const errorMessage =
            response.error instanceof Error
              ? response.error.message
              : "Failed to search experiences"
          setSearchError(errorMessage)
          toast.error("Failed to search experiences")
        }
      } catch (error) {
        console.error("Error searching experiences:", error)
        setSearchError("An unexpected error occurred")
        toast.error("An unexpected error occurred while searching")
      } finally {
        setIsSearching(false)
      }
    },
    [
      experiences,
      searchResult,
      setExperiences,
      setSearchFilters,
      setSearchResult,
      setIsSearching,
      setSearchError,
    ]
  )

  /**
   * Load all experiences (initial load)
   */
  const loadAllExperiences = useCallback(async () => {
    try {
      setIsSearching(true)
      setSearchError(null)

      const response = await LocalExperiencesService.getAllExperiences()

      if (response.success && response.data) {
        setExperiences(response.data)
        setSearchResult({
          experiences: response.data,
          total: response.data.length,
          hasMore: false,
        })
      } else {
        const errorMessage =
          response.error instanceof Error ? response.error.message : "Failed to load experiences"
        setSearchError(errorMessage)
        toast.error("Failed to load experiences")
      }
    } catch (error) {
      console.error("Error loading experiences:", error)
      setSearchError("An unexpected error occurred")
      toast.error("An unexpected error occurred while loading experiences")
    } finally {
      setIsSearching(false)
    }
  }, [setExperiences, setSearchResult, setIsSearching, setSearchError])

  /**
   * Clear search results and filters
   */
  const clearSearch = useCallback(() => {
    clearSearchResults()
    setSearchFilters({})
  }, [clearSearchResults, setSearchFilters])

  /**
   * Load more experiences (pagination)
   */
  const loadMoreExperiences = useCallback(() => {
    if (searchResult && searchResult.hasMore && !isSearching) {
      searchExperiences(searchFilters, true)
    }
  }, [searchResult, isSearching, searchFilters, searchExperiences])

  return {
    // State
    experiences,
    searchFilters,
    searchResult,
    isSearching,
    searchError,
    hasMore: searchResult?.hasMore || false,

    // Actions
    searchExperiences,
    loadAllExperiences,
    clearSearch,
    loadMoreExperiences,
    updateSearchFilters: setSearchFilters,
  }
}

/**
 * Hook for managing a single experience
 */
export const useLocalExperience = () => {
  const {
    selectedExperience,
    selectedExperienceAvailability,
    isLoadingExperience,
    experienceError,
    isLoadingAvailability,
    availabilityError,
    setSelectedExperience,
    setSelectedExperienceAvailability,
    setIsLoadingExperience,
    setExperienceError,
    setIsLoadingAvailability,
    setAvailabilityError,
  } = useLocalExperiencesStore()

  /**
   * Load experience by ID
   */
  const loadExperience = useCallback(
    async (experienceId: string) => {
      try {
        setIsLoadingExperience(true)
        setExperienceError(null)

        const response = await LocalExperiencesService.getExperience(experienceId)

        if (response.success && response.data) {
          setSelectedExperience(response.data)
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to load experience"
          setExperienceError(errorMessage)
          toast.error("Failed to load experience details")
        }
      } catch (error) {
        console.error("Error loading experience:", error)
        setExperienceError("An unexpected error occurred")
        toast.error("An unexpected error occurred while loading experience")
      } finally {
        setIsLoadingExperience(false)
      }
    },
    [setSelectedExperience, setIsLoadingExperience, setExperienceError]
  )

  /**
   * Load experience availability for a specific date
   */
  const loadExperienceAvailability = useCallback(
    async (experienceId: string, date: string) => {
      try {
        setIsLoadingAvailability(true)
        setAvailabilityError(null)

        const response = await LocalExperiencesService.getExperienceAvailability(experienceId, date)

        if (response.success) {
          setSelectedExperienceAvailability(response.data || null)
        } else {
          const errorMessage =
            response.error instanceof Error ? response.error.message : "Failed to load availability"
          setAvailabilityError(errorMessage)
          toast.error("Failed to load availability")
        }
      } catch (error) {
        console.error("Error loading availability:", error)
        setAvailabilityError("An unexpected error occurred")
        toast.error("An unexpected error occurred while loading availability")
      } finally {
        setIsLoadingAvailability(false)
      }
    },
    [setSelectedExperienceAvailability, setIsLoadingAvailability, setAvailabilityError]
  )

  /**
   * Select an experience (for modal display)
   */
  const selectExperience = useCallback(
    (experience: LocalExperience) => {
      setSelectedExperience(experience)
      setExperienceError(null)
    },
    [setSelectedExperience, setExperienceError]
  )

  /**
   * Clear selected experience
   */
  const clearSelectedExperience = useCallback(() => {
    setSelectedExperience(null)
    setSelectedExperienceAvailability(null)
    setExperienceError(null)
    setAvailabilityError(null)
  }, [
    setSelectedExperience,
    setSelectedExperienceAvailability,
    setExperienceError,
    setAvailabilityError,
  ])

  return {
    // State
    selectedExperience,
    selectedExperienceAvailability,
    isLoadingExperience,
    experienceError,
    isLoadingAvailability,
    availabilityError,

    // Actions
    loadExperience,
    loadExperienceAvailability,
    selectExperience,
    clearSelectedExperience,
  }
}

/**
 * Hook for managing experience modal state
 */
export const useExperienceModal = () => {
  const {
    isExperienceModalOpen,
    isBookingModalOpen,
    selectedExperience,
    openExperienceModal,
    closeExperienceModal,
    openBookingModal,
    closeBookingModal,
  } = useLocalExperiencesStore()

  return {
    // State
    isExperienceModalOpen,
    isBookingModalOpen,
    selectedExperience,

    // Actions
    openExperienceModal,
    closeExperienceModal,
    openBookingModal,
    closeBookingModal,
  }
}
