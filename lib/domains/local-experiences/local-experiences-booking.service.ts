import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  QueryDocumentSnapshot,
  serverTimestamp,
  setDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  ExperienceBooking,
  ExperienceBookingCreateData,
  ExperienceBookingUpdateData,
  BookingHistoryFilters,
  BookingSearchResult,
  BookingStatus,
} from "./local-experiences-booking.types"

/**
 * Experience Booking service for Firebase operations
 */
export class LocalExperiencesBookingService extends BaseService {
  private static readonly EXPERIENCES_COLLECTION = "localExperiences"
  private static readonly BOOKINGS_SUBCOLLECTION = "bookings"

  constructor() {
    super()
  }

  /**
   * Create a new booking
   */
  static async createBooking(
    experienceId: string,
    bookingData: ExperienceBookingCreateData
  ): Promise<ServiceResponse<string>> {
    try {
      const bookingRef = doc(
        collection(db, this.EXPERIENCES_COLLECTION, experienceId, this.BOOKINGS_SUBCOLLECTION)
      )
      const bookingId = bookingRef.id

      const newBooking: ExperienceBooking = {
        ...bookingData,
        id: bookingId,
        createdAt: serverTimestamp() as Timestamp,
        status: "pending",
        paymentStatus: "pending",
        bookedAt: serverTimestamp() as Timestamp,
      }

      await setDoc(bookingRef, newBooking)
      return { success: true, data: bookingId }
    } catch (error) {
      console.error("Error creating booking:", error)
      return { success: false, error: "Failed to create booking" }
    }
  }

  /**
   * Get booking by ID
   */
  static async getBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<ExperienceBooking>> {
    try {
      const bookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      const bookingSnap = await getDoc(bookingRef)

      if (!bookingSnap.exists()) {
        return { success: false, error: "Booking not found" }
      }

      const booking = { id: bookingSnap.id, ...bookingSnap.data() } as ExperienceBooking
      return { success: true, data: booking }
    } catch (error) {
      console.error("Error getting booking:", error)
      return { success: false, error: "Failed to get booking" }
    }
  }

  /**
   * Update booking
   */
  static async updateBooking(
    experienceId: string,
    bookingId: string,
    updateData: ExperienceBookingUpdateData
  ): Promise<ServiceResponse<void>> {
    try {
      const bookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      await updateDoc(bookingRef, updateData)
      return { success: true }
    } catch (error) {
      console.error("Error updating booking:", error)
      return { success: false, error: "Failed to update booking" }
    }
  }

  /**
   * Confirm booking (after successful payment)
   */
  static async confirmBooking(
    experienceId: string,
    bookingId: string,
    paymentDetails: {
      stripeSessionId?: string
      stripePaymentIntentId?: string
      stripeCustomerId?: string
    }
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "confirmed",
        paymentStatus: "paid",
        confirmedAt: serverTimestamp() as Timestamp,
        ...paymentDetails,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error confirming booking:", error)
      return { success: false, error: "Failed to confirm booking" }
    }
  }

  /**
   * Cancel booking
   */
  static async cancelBooking(
    experienceId: string,
    bookingId: string,
    reason?: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "cancelled",
        cancelledAt: serverTimestamp() as Timestamp,
        cancellationReason: reason,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error cancelling booking:", error)
      return { success: false, error: "Failed to cancel booking" }
    }
  }

  /**
   * Complete booking (after experience is finished)
   */
  static async completeBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const updateData: ExperienceBookingUpdateData = {
        status: "completed",
        completedAt: serverTimestamp() as Timestamp,
      }

      return await this.updateBooking(experienceId, bookingId, updateData)
    } catch (error) {
      console.error("Error completing booking:", error)
      return { success: false, error: "Failed to complete booking" }
    }
  }

  /**
   * Get user's booking history
   */
  static async getUserBookings(
    _userId: string,
    _filters?: BookingHistoryFilters,
    _pageSize: number = 10,
    _lastDoc?: QueryDocumentSnapshot
  ): Promise<ServiceResponse<BookingSearchResult>> {
    try {
      // Note: This is a complex query that would require a collection group query
      // For now, we'll implement a simpler version that searches across all experiences
      // In production, you might want to maintain a separate user-bookings collection

      // This is a simplified implementation - in reality, you'd need to use collection group queries
      // or maintain a separate index for user bookings
      const bookings: ExperienceBooking[] = []

      // TODO: Implement proper collection group query for user bookings
      // For now, return empty result
      return {
        success: true,
        data: {
          bookings,
          total: 0,
          hasMore: false,
        },
      }
    } catch (error) {
      console.error("Error getting user bookings:", error)
      return { success: false, error: "Failed to get user bookings" }
    }
  }

  /**
   * Get bookings for an experience
   */
  static async getExperienceBookings(
    experienceId: string,
    status?: BookingStatus[],
    pageSize: number = 10
  ): Promise<ServiceResponse<ExperienceBooking[]>> {
    try {
      let bookingsQuery = query(
        collection(db, this.EXPERIENCES_COLLECTION, experienceId, this.BOOKINGS_SUBCOLLECTION),
        orderBy("bookedAt", "desc")
      )

      if (status && status.length > 0) {
        bookingsQuery = query(bookingsQuery, where("status", "in", status))
      }

      bookingsQuery = query(bookingsQuery, limit(pageSize))

      const querySnapshot = await getDocs(bookingsQuery)
      const bookings: ExperienceBooking[] = querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as ExperienceBooking
      )

      return { success: true, data: bookings }
    } catch (error) {
      console.error("Error getting experience bookings:", error)
      return { success: false, error: "Failed to get experience bookings" }
    }
  }

  /**
   * Delete booking (admin only)
   */
  static async deleteBooking(
    experienceId: string,
    bookingId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const bookingRef = doc(
        db,
        this.EXPERIENCES_COLLECTION,
        experienceId,
        this.BOOKINGS_SUBCOLLECTION,
        bookingId
      )
      await deleteDoc(bookingRef)
      return { success: true }
    } catch (error) {
      console.error("Error deleting booking:", error)
      return { success: false, error: "Failed to delete booking" }
    }
  }
}
