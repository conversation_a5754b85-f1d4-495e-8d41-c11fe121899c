import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  QueryDocumentSnapshot,
  serverTimestamp,
  setDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  LocalExperience,
  LocalExperienceCreateData,
  LocalExperienceUpdateData,
  ExperienceSearchFilters,
  ExperienceSearchResult,
  ExperienceAvailability,
} from "./local-experiences.types"

/**
 * Local Experiences service for Firebase operations
 */
export class LocalExperiencesService extends BaseService {
  private static readonly COLLECTION = "localExperiences"
  private static readonly AVAILABILITY_SUBCOLLECTION = "availability"

  constructor() {
    super()
  }

  /**
   * Create a new experience
   */
  static async createExperience(experienceData: LocalExperienceCreateData): Promise<string> {
    try {
      const experienceRef = doc(collection(db, this.COLLECTION))
      const experienceId = experienceRef.id

      const newExperience: LocalExperience = {
        ...experienceData,
        id: experienceId,
        createdAt: serverTimestamp() as Timestamp,
        rating: 0,
        reviewCount: 0,
      }

      await setDoc(experienceRef, newExperience)
      return experienceId
    } catch (error) {
      console.error("Error creating experience:", error)
      throw error
    }
  }

  /**
   * Get experience by ID
   */
  static async getExperience(experienceId: string): Promise<ServiceResponse<LocalExperience>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      const experienceSnap = await getDoc(experienceRef)

      if (!experienceSnap.exists()) {
        return { success: false, error: "Experience not found" }
      }

      const experience = { id: experienceSnap.id, ...experienceSnap.data() } as LocalExperience
      return { success: true, data: experience }
    } catch (error) {
      console.error("Error getting experience:", error)
      return { success: false, error: "Failed to get experience" }
    }
  }

  /**
   * Search experiences with filters
   */
  static async searchExperiences(
    filters: ExperienceSearchFilters,
    pageSize: number = 10,
    lastDoc?: QueryDocumentSnapshot
  ): Promise<ServiceResponse<ExperienceSearchResult>> {
    try {
      let experienceQuery = query(collection(db, this.COLLECTION), where("isActive", "==", true))

      // Apply filters
      if (filters.location) {
        experienceQuery = query(experienceQuery, where("location.city", "==", filters.location))
      }

      if (filters.categories && filters.categories.length > 0) {
        experienceQuery = query(
          experienceQuery,
          where("categories", "array-contains-any", filters.categories)
        )
      }

      if (filters.priceRange) {
        if (filters.priceRange.min) {
          experienceQuery = query(
            experienceQuery,
            where("pricing.basePrice", ">=", filters.priceRange.min)
          )
        }
        if (filters.priceRange.max) {
          experienceQuery = query(
            experienceQuery,
            where("pricing.basePrice", "<=", filters.priceRange.max)
          )
        }
      }

      // Apply sorting
      switch (filters.sortBy) {
        case "price_low":
          experienceQuery = query(experienceQuery, orderBy("pricing.basePrice", "asc"))
          break
        case "price_high":
          experienceQuery = query(experienceQuery, orderBy("pricing.basePrice", "desc"))
          break
        case "rating":
          experienceQuery = query(experienceQuery, orderBy("rating", "desc"))
          break
        case "newest":
        default:
          experienceQuery = query(experienceQuery, orderBy("createdAt", "desc"))
          break
      }

      // Apply pagination
      if (lastDoc) {
        experienceQuery = query(experienceQuery, startAfter(lastDoc))
      }
      experienceQuery = query(experienceQuery, limit(pageSize + 1)) // +1 to check if there are more

      const querySnapshot = await getDocs(experienceQuery)
      const experiences: LocalExperience[] = []
      const docs = querySnapshot.docs

      // Process results
      for (let i = 0; i < Math.min(docs.length, pageSize); i++) {
        const doc = docs[i]
        experiences.push({ id: doc.id, ...doc.data() } as LocalExperience)
      }

      const hasMore = docs.length > pageSize
      const total = experiences.length // Note: This is not the total count, just current page

      return {
        success: true,
        data: {
          experiences,
          total,
          hasMore,
        },
      }
    } catch (error) {
      console.error("Error searching experiences:", error)
      return { success: false, error: "Failed to search experiences" }
    }
  }

  /**
   * Get all experiences (for admin/management)
   */
  static async getAllExperiences(): Promise<ServiceResponse<LocalExperience[]>> {
    try {
      const experiencesQuery = query(collection(db, this.COLLECTION), orderBy("createdAt", "desc"))
      const querySnapshot = await getDocs(experiencesQuery)

      const experiences: LocalExperience[] = querySnapshot.docs.map(
        (doc) =>
          ({
            id: doc.id,
            ...doc.data(),
          }) as LocalExperience
      )

      return { success: true, data: experiences }
    } catch (error) {
      console.error("Error getting all experiences:", error)
      return { success: false, error: "Failed to get experiences" }
    }
  }

  /**
   * Update experience
   */
  static async updateExperience(
    experienceId: string,
    updateData: LocalExperienceUpdateData
  ): Promise<ServiceResponse<void>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      await updateDoc(experienceRef, updateData)
      return { success: true }
    } catch (error) {
      console.error("Error updating experience:", error)
      return { success: false, error: "Failed to update experience" }
    }
  }

  /**
   * Delete experience
   */
  static async deleteExperience(experienceId: string): Promise<ServiceResponse<void>> {
    try {
      const experienceRef = doc(db, this.COLLECTION, experienceId)
      await deleteDoc(experienceRef)
      return { success: true }
    } catch (error) {
      console.error("Error deleting experience:", error)
      return { success: false, error: "Failed to delete experience" }
    }
  }

  /**
   * Get experience availability for a specific date
   */
  static async getExperienceAvailability(
    experienceId: string,
    date: string
  ): Promise<ServiceResponse<ExperienceAvailability | null>> {
    try {
      const availabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        date
      )
      const availabilitySnap = await getDoc(availabilityRef)

      if (!availabilitySnap.exists()) {
        return { success: true, data: null }
      }

      const availability = availabilitySnap.data() as ExperienceAvailability
      return { success: true, data: availability }
    } catch (error) {
      console.error("Error getting experience availability:", error)
      return { success: false, error: "Failed to get availability" }
    }
  }

  /**
   * Update experience availability
   */
  static async updateExperienceAvailability(
    experienceId: string,
    availability: ExperienceAvailability
  ): Promise<ServiceResponse<void>> {
    try {
      const availabilityRef = doc(
        db,
        this.COLLECTION,
        experienceId,
        this.AVAILABILITY_SUBCOLLECTION,
        availability.date
      )
      await setDoc(availabilityRef, availability)
      return { success: true }
    } catch (error) {
      console.error("Error updating experience availability:", error)
      return { success: false, error: "Failed to update availability" }
    }
  }
}
