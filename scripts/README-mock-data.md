# Mock Experience Data Setup

This directory contains mock data for the Local Experiences feature.

## Files

- `mock-experiences.json` - Contains 3 sample experiences with complete data
- `add-mock-experiences.js` - Node.js script to add data to Firestore (requires authentication)

## Manual Setup Instructions

Since the automated script requires Firebase authentication, you can manually add the mock data:

### Option 1: Using Firebase Console

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Select your project (`brotrip-mvp`)
3. Navigate to Firestore Database
4. Create a new collection called `localExperiences`
5. For each experience in `mock-experiences.json`, create a new document with the provided data

### Option 2: Using Firebase CLI (if authenticated)

1. First, authenticate with Firebase:

   ```bash
   firebase login
   ```

2. Then run the script:
   ```bash
   node scripts/add-mock-experiences.js
   ```

## Mock Experiences Included

1. **Sunset Kayaking Adventure** - $89

   - 3-hour guided kayaking tour in San Francisco Bay
   - Host: <PERSON>
   - Categories: adventure, outdoor

2. **Authentic Italian Cooking Class** - $125

   - 4-hour hands-on cooking class in North Beach
   - Host: <PERSON>
   - Categories: food, culture, indoor

3. **Street Art & Murals Walking Tour** - $45
   - 2.5-hour guided tour through Mission District
   - Host: Carlos <PERSON>
   - Categories: culture, outdoor

Each experience includes:

- Complete host information
- Detailed location data
- Pricing breakdown
- Availability time slots
- High-quality images
- Inclusions/exclusions
- Cancellation policies
- Sample ratings and reviews

## Availability Data

The script also creates sample availability data for the next 30 days with appropriate time slots for each experience type.
